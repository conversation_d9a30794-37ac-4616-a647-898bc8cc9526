#!/usr/bin/env node

/**
 * Debug script to test @xenova/transformers loading in different environments
 * Run this script to test if transformers can be loaded properly
 */

const path = require('path');
const fs = require('fs');

console.log('🔍 Debugging @xenova/transformers loading...\n');

// Test 1: Check if we're in a packaged extension environment
console.log('📍 Environment Check:');
console.log('- __dirname:', __dirname);
console.log('- process.cwd():', process.cwd());
console.log('- Node.js version:', process.version);
console.log('- Platform:', process.platform);
console.log('');

// Test 2: Check for node_modules directories
console.log('📁 Node Modules Check:');
const possibleNodeModules = [
  path.join(__dirname, 'node_modules'),
  path.join(__dirname, 'dist', 'node_modules'),
  path.join(__dirname, 'dist', 'workers', 'node_modules'),
  path.join(__dirname, '..', 'node_modules'),
  path.join(__dirname, '..', '..', 'node_modules'),
];

for (const nmPath of possibleNodeModules) {
  const exists = fs.existsSync(nmPath);
  console.log(`- ${nmPath}: ${exists ? '✅ EXISTS' : '❌ NOT FOUND'}`);
  
  if (exists) {
    const xenovaPath = path.join(nmPath, '@xenova', 'transformers');
    const xenovaExists = fs.existsSync(xenovaPath);
    console.log(`  └─ @xenova/transformers: ${xenovaExists ? '✅ EXISTS' : '❌ NOT FOUND'}`);
    
    if (xenovaExists) {
      const packageJsonPath = path.join(xenovaPath, 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        try {
          const pkg = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
          console.log(`     Version: ${pkg.version}`);
          console.log(`     Main: ${pkg.main}`);
          console.log(`     Type: ${pkg.type}`);
        } catch (e) {
          console.log(`     ❌ Error reading package.json: ${e.message}`);
        }
      }
    }
  }
}
console.log('');

// Test 3: Try different import methods
console.log('🧪 Import Tests:');

async function testImports() {
  // Test 1: Direct require (CommonJS)
  try {
    console.log('1. Testing require("@xenova/transformers")...');
    const transformers = require('@xenova/transformers');
    console.log('   ✅ SUCCESS - CommonJS require worked');
    console.log('   - pipeline function:', typeof transformers.pipeline);
  } catch (error) {
    console.log('   ❌ FAILED - CommonJS require failed:', error.message);
  }

  // Test 2: Dynamic import (ES modules)
  try {
    console.log('2. Testing import("@xenova/transformers")...');
    const transformers = await import('@xenova/transformers');
    console.log('   ✅ SUCCESS - ES module import worked');
    console.log('   - pipeline function:', typeof transformers.pipeline);
  } catch (error) {
    console.log('   ❌ FAILED - ES module import failed:', error.message);
  }

  // Test 3: Try specific paths
  const testPaths = [
    path.join(__dirname, 'dist', 'node_modules', '@xenova', 'transformers'),
    path.join(__dirname, 'dist', 'workers', 'node_modules', '@xenova', 'transformers'),
    path.join(__dirname, 'node_modules', '@xenova', 'transformers'),
  ];

  for (let i = 0; i < testPaths.length; i++) {
    const testPath = testPaths[i];
    if (fs.existsSync(testPath)) {
      try {
        console.log(`${3 + i}. Testing import("${testPath}")...`);
        const transformers = await import(testPath);
        console.log('   ✅ SUCCESS - Direct path import worked');
        console.log('   - pipeline function:', typeof transformers.pipeline);
        break;
      } catch (error) {
        console.log(`   ❌ FAILED - Direct path import failed: ${error.message}`);
      }
    }
  }
}

// Test 4: Test pipeline creation
async function testPipeline() {
  console.log('\n🚀 Pipeline Creation Test:');
  
  try {
    // Try to load transformers first
    let pipeline;
    try {
      const transformers = await import('@xenova/transformers');
      pipeline = transformers.pipeline;
    } catch (e) {
      console.log('   Using fallback path resolution...');
      const fallbackPath = path.join(__dirname, 'dist', 'node_modules', '@xenova', 'transformers');
      const transformers = await import(fallbackPath);
      pipeline = transformers.pipeline;
    }

    console.log('   Creating feature extraction pipeline...');
    const extractor = await pipeline('feature-extraction', 'jinaai/jina-embeddings-v2-base-code');
    console.log('   ✅ SUCCESS - Pipeline created successfully');
    console.log('   - Extractor type:', typeof extractor);
    
    // Test a simple embedding
    console.log('   Testing embedding generation...');
    const result = await extractor('Hello world', { pooling: 'mean', normalize: true });
    console.log('   ✅ SUCCESS - Embedding generated');
    console.log('   - Result shape:', result.data.length);
    
  } catch (error) {
    console.log('   ❌ FAILED - Pipeline creation failed:', error.message);
    console.log('   - Stack:', error.stack);
  }
}

// Run all tests
async function runAllTests() {
  await testImports();
  await testPipeline();
  
  console.log('\n🎯 Summary:');
  console.log('If you see failures above, the issue is likely:');
  console.log('1. Missing dependencies in the packaged extension');
  console.log('2. Module resolution issues in the worker processes');
  console.log('3. ES module vs CommonJS compatibility problems');
  console.log('\nTo fix:');
  console.log('- Ensure all dependencies are copied to dist/node_modules and dist/workers/node_modules');
  console.log('- Use the enhanced module loading in worker files');
  console.log('- Check VSCode extension host environment compatibility');
}

runAllTests().catch(console.error);
